[tool:pytest]
minversion = 6.0
addopts = 
    -ra
    --strict-markers
    --strict-config
    --cov=app
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --cov-fail-under=90
    --disable-warnings
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    api: marks tests as API tests
    service: marks tests as service layer tests
    database: marks tests as database tests
    auth: marks tests as authentication tests
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function
env = 
    ENVIRONMENT_NAME = test
    AWS_REGION = us-east-1
    LOCAL_DB_URL = mongodb://localhost:27017/test_db
    redis_host = localhost
    AZURE_OPENAI_API_KEY = test_key
    AZURE_OPENAI_ENDPOINT = https://test.openai.azure.com/
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning

