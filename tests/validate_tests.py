#!/usr/bin/env python3
"""
Test validation script to check our test structure and coverage.
"""
import os
import ast
import sys
from pathlib import Path


def count_test_functions(file_path):
    """Count test functions in a Python file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        test_functions = []
        test_classes = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef) and node.name.startswith('test_'):
                test_functions.append(node.name)
            elif isinstance(node, ast.ClassDef) and node.name.startswith('Test'):
                test_classes.append(node.name)
                # Count methods in test classes
                for item in node.body:
                    if isinstance(item, ast.FunctionDef) and item.name.startswith('test_'):
                        test_functions.append(f"{node.name}.{item.name}")
        
        return test_functions, test_classes
    except Exception as e:
        print(f"Error parsing {file_path}: {e}")
        return [], []


def analyze_test_coverage():
    """Analyze test coverage across the codebase."""
    test_dir = Path("tests")
    app_dir = Path("app")
    
    if not test_dir.exists():
        print("❌ Tests directory not found!")
        return False
    
    if not app_dir.exists():
        print("❌ App directory not found!")
        return False
    
    print("🔍 Analyzing Test Structure")
    print("=" * 50)
    
    # Find all test files
    test_files = list(test_dir.rglob("test_*.py"))
    total_test_functions = 0
    total_test_classes = 0
    
    print(f"📁 Found {len(test_files)} test files:")
    
    for test_file in test_files:
        functions, classes = count_test_functions(test_file)
        total_test_functions += len(functions)
        total_test_classes += len(classes)
        
        print(f"  📄 {test_file}")
        print(f"     Classes: {len(classes)}, Functions: {len(functions)}")
        
        if len(functions) > 0:
            print(f"     Test functions: {', '.join(functions[:3])}{'...' if len(functions) > 3 else ''}")
    
    print("\n📊 Test Summary:")
    print(f"  Total test files: {len(test_files)}")
    print(f"  Total test classes: {total_test_classes}")
    print(f"  Total test functions: {total_test_functions}")
    
    # Find all app modules
    app_files = list(app_dir.rglob("*.py"))
    app_modules = [f for f in app_files if not f.name.startswith('__')]
    
    print(f"\n📁 Found {len(app_modules)} app modules:")
    
    # Check coverage mapping
    covered_modules = set()
    for test_file in test_files:
        # Extract module name from test file
        test_name = test_file.stem
        if test_name.startswith('test_'):
            module_name = test_name[5:]  # Remove 'test_' prefix
            covered_modules.add(module_name)
    
    print(f"  Modules with tests: {len(covered_modules)}")
    print(f"  Coverage ratio: {len(covered_modules)}/{len(app_modules)} ({len(covered_modules)/len(app_modules)*100:.1f}%)")
    
    return True


def check_test_structure():
    """Check test structure and organization."""
    print("\n🏗️  Test Structure Analysis")
    print("=" * 50)
    
    required_files = [
        "tests/conftest.py",
        "tests/test_setup.py",
        "pytest.ini",
        "run_tests.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    if missing_files:
        print(f"❌ Missing files: {', '.join(missing_files)}")
        return False
    
    # Check test directories
    test_dirs = [
        "tests/test_api",
        "tests/test_services", 
        "tests/test_core",
        "tests/test_database",
        "tests/test_models"
    ]
    
    print("\n📁 Test directories:")
    for test_dir in test_dirs:
        if Path(test_dir).exists():
            test_files = list(Path(test_dir).glob("test_*.py"))
            print(f"✅ {test_dir} ({len(test_files)} files)")
        else:
            print(f"❌ {test_dir} (missing)")
    
    return True


def check_pytest_config():
    """Check pytest configuration."""
    print("\n⚙️  Pytest Configuration")
    print("=" * 50)
    
    pytest_ini = Path("pytest.ini")
    if pytest_ini.exists():
        print("✅ pytest.ini found")
        
        with open(pytest_ini, 'r') as f:
            content = f.read()
            
        # Check for important configurations
        configs = [
            ("coverage", "--cov" in content),
            ("test paths", "testpaths" in content),
            ("markers", "markers" in content),
            ("async support", "asyncio" in content)
        ]
        
        for config_name, found in configs:
            status = "✅" if found else "❌"
            print(f"  {status} {config_name}")
    else:
        print("❌ pytest.ini not found")
        return False
    
    return True


def estimate_coverage():
    """Estimate test coverage based on file structure."""
    print("\n📈 Coverage Estimation")
    print("=" * 50)
    
    # Key components to test
    components = {
        "API Controllers": ["tests/test_api/test_classifier_controller.py", "tests/test_api/test_query_controller.py"],
        "Services": ["tests/test_services/test_classifier_service.py", "tests/test_services/test_query_service.py", "tests/test_services/test_simplrops_context_service.py"],
        "Authentication": ["tests/test_services/test_auth_service.py"],
        "Database": ["tests/test_database/test_database.py"],
        "Models": ["tests/test_models/test_schemas.py"],
        "Core Config": ["tests/test_core/test_config.py"],
        "Server": ["tests/test_server.py"],
        "Utils": ["tests/test_services/test_utils.py"]
    }
    
    total_components = len(components)
    covered_components = 0
    
    for component, test_files in components.items():
        all_exist = all(Path(f).exists() for f in test_files)
        status = "✅" if all_exist else "❌"
        covered_components += 1 if all_exist else 0
        
        print(f"  {status} {component}")
        for test_file in test_files:
            file_status = "✅" if Path(test_file).exists() else "❌"
            print(f"    {file_status} {test_file}")
    
    coverage_percent = (covered_components / total_components) * 100
    print(f"\n📊 Estimated Coverage: {covered_components}/{total_components} ({coverage_percent:.1f}%)")
    
    if coverage_percent >= 90:
        print("🎉 Excellent coverage! Target achieved.")
    elif coverage_percent >= 70:
        print("👍 Good coverage, close to target.")
    else:
        print("⚠️  Coverage needs improvement.")
    
    return coverage_percent >= 90


def main():
    """Main validation function."""
    print("SimplrOps Copilot - Test Validation")
    print("=" * 60)
    
    success = True
    
    # Run all checks
    success &= analyze_test_coverage()
    success &= check_test_structure()
    success &= check_pytest_config()
    success &= estimate_coverage()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ Test structure validation PASSED!")
        print("🚀 Ready to run comprehensive tests.")
    else:
        print("❌ Test structure validation FAILED!")
        print("🔧 Please fix the issues above.")
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())

