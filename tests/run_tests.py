#!/usr/bin/env python3
"""
Test runner script for SimplrOps Copilot.
Runs pytest with coverage and generates reports.
"""
import os
import sys
import subprocess
import argparse
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {' '.join(command)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        return True
    except subprocess.CalledProcessError as e:
        print(f"ERROR: {description} failed!")
        print(f"Return code: {e.returncode}")
        print(f"STDOUT: {e.stdout}")
        print(f"STDERR: {e.stderr}")
        return False


def install_dependencies():
    """Install test dependencies."""
    print("Installing test dependencies...")
    return run_command(
        [sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
        "Installing dependencies"
    )


def run_tests(test_type="all", verbose=False, coverage=True, parallel=False):
    """Run tests with specified options."""
    # Base pytest command
    cmd = [sys.executable, "-m", "pytest"]
    
    # Add verbosity
    if verbose:
        cmd.append("-v")
    else:
        cmd.append("-q")
    
    # Add coverage
    if coverage:
        cmd.extend([
            "--cov=app",
            "--cov-report=term-missing",
            "--cov-report=html:htmlcov",
            "--cov-report=xml",
            "--cov-fail-under=90"
        ])
    
    # Add parallel execution
    if parallel:
        cmd.extend(["-n", "auto"])
    
    # Add test selection
    if test_type == "unit":
        cmd.extend(["-m", "unit"])
    elif test_type == "integration":
        cmd.extend(["-m", "integration"])
    elif test_type == "api":
        cmd.extend(["-m", "api"])
    elif test_type == "service":
        cmd.extend(["-m", "service"])
    elif test_type == "fast":
        cmd.extend(["-m", "not slow"])
    
    # Add test directory
    cmd.append("tests/")
    
    return run_command(cmd, f"Running {test_type} tests")


def run_linting():
    """Run code linting."""
    commands = [
        ([sys.executable, "-m", "flake8", "app/", "tests/"], "Flake8 linting"),
        ([sys.executable, "-m", "black", "--check", "app/", "tests/"], "Black formatting check"),
        ([sys.executable, "-m", "isort", "--check-only", "app/", "tests/"], "Import sorting check")
    ]
    
    all_passed = True
    for cmd, description in commands:
        try:
            if not run_command(cmd, description):
                all_passed = False
        except FileNotFoundError:
            print(f"Skipping {description} - tool not installed")
    
    return all_passed


def generate_coverage_report():
    """Generate and display coverage report."""
    print("\n" + "="*60)
    print("COVERAGE SUMMARY")
    print("="*60)
    
    # Generate coverage report
    run_command(
        [sys.executable, "-m", "coverage", "report", "--show-missing"],
        "Generating coverage report"
    )
    
    # Check if HTML report was generated
    html_report_path = Path("htmlcov/index.html")
    if html_report_path.exists():
        print(f"\nHTML coverage report generated: {html_report_path.absolute()}")
        print("Open this file in a browser to view detailed coverage information.")


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(description="SimplrOps Copilot Test Runner")
    parser.add_argument(
        "--type", 
        choices=["all", "unit", "integration", "api", "service", "fast"],
        default="all",
        help="Type of tests to run"
    )
    parser.add_argument(
        "--no-coverage", 
        action="store_true",
        help="Skip coverage reporting"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Verbose output"
    )
    parser.add_argument(
        "--parallel", "-p",
        action="store_true",
        help="Run tests in parallel"
    )
    parser.add_argument(
        "--install-deps",
        action="store_true",
        help="Install dependencies before running tests"
    )
    parser.add_argument(
        "--lint",
        action="store_true",
        help="Run linting checks"
    )
    parser.add_argument(
        "--quick",
        action="store_true",
        help="Quick test run (unit tests only, no coverage)"
    )
    
    args = parser.parse_args()
    
    # Set up quick mode
    if args.quick:
        args.type = "unit"
        args.no_coverage = True
    
    print("SimplrOps Copilot Test Runner")
    print("="*60)
    print(f"Test type: {args.type}")
    print(f"Coverage: {'No' if args.no_coverage else 'Yes'}")
    print(f"Verbose: {'Yes' if args.verbose else 'No'}")
    print(f"Parallel: {'Yes' if args.parallel else 'No'}")
    
    success = True
    
    # Install dependencies if requested
    if args.install_deps:
        if not install_dependencies():
            print("Failed to install dependencies!")
            return 1
    
    # Run linting if requested
    if args.lint:
        if not run_linting():
            print("Linting checks failed!")
            success = False
    
    # Run tests
    if not run_tests(
        test_type=args.type,
        verbose=args.verbose,
        coverage=not args.no_coverage,
        parallel=args.parallel
    ):
        print("Tests failed!")
        success = False
    
    # Generate coverage report if coverage was enabled
    if not args.no_coverage and success:
        generate_coverage_report()
    
    # Final summary
    print("\n" + "="*60)
    if success:
        print("✅ ALL TESTS PASSED!")
        print("="*60)
        return 0
    else:
        print("❌ SOME TESTS FAILED!")
        print("="*60)
        return 1


if __name__ == "__main__":
    sys.exit(main())

